//
//  ProjectEditingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Combine
import ObjectiveC

struct ProjectEditingView: View {
    let project: Project?
    let projectManager: ProjectManager
    
    @Environment(\.presentationMode) var presentationMode
    @State private var contentView: ContentView?
    @State private var autoSaveTimer: Timer?
    @State private var lastSaveTime = Date()
    
    // 自动保存间隔（秒）
    private let autoSaveInterval: TimeInterval = 3.0
    
    init(project: Project? = nil, projectManager: ProjectManager) {
        self.project = project
        self.projectManager = projectManager
    }
    
    var body: some View {
        ZStack {
            if let contentView = contentView {
                contentView
                    .onAppear {
                        setupProject(contentView: contentView)
                        startAutoSave(contentView: contentView)
                    }
                    .onDisappear {
                        stopAutoSave()
                        // 最后保存一次
                        performAutoSave(contentView: contentView, actionDescription: "退出编辑")
                    }
            } else {
                // 加载中状态
                ZStack {
                    LinearGradient(
                        colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                    
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: CyberPunkStyle.neonPink))
                            .scaleEffect(1.5)
                        
                        Text("正在加载项目...")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
        .onAppear {
            // 创建 ContentView 实例
            contentView = ContentView(showBackButton: true)
        }
    }
    
    // 设置项目
    private func setupProject(contentView: ContentView) {
        if let project = project {
            // 恢复现有项目
            restoreProject(project, to: contentView)
        } else {
            // 新项目，设置为当前项目
            if let currentProject = projectManager.currentProject {
                restoreProject(currentProject, to: contentView)
            }
        }
        
        // 设置项目管理器引用
        contentView.projectManager = projectManager
        contentView.currentProject = project ?? projectManager.currentProject
    }
    
    // 恢复项目数据
    private func restoreProject(_ project: Project, to contentView: ContentView) {
        // 获取最新的编辑步骤
        let steps = projectManager.getEditSteps(for: project.id)
        if let latestStep = steps.last {
            // 恢复图层
            let restoredLayers = latestStep.restoreLayers()
            contentView.processor.layers = restoredLayers
            
            // 恢复画布设置
            contentView.canvasSize = latestStep.canvasSize
            contentView.displayCanvasSize = latestStep.canvasSize
            
            // 恢复画布比例
            if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == latestStep.aspectRatio }) {
                contentView.selectedAspectRatio = aspectRatio
            }
        } else {
            // 如果没有步骤，使用项目的基本设置
            contentView.canvasSize = project.canvasSize
            contentView.displayCanvasSize = project.canvasSize
            
            if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == project.aspectRatio }) {
                contentView.selectedAspectRatio = aspectRatio
            }
        }
        
        // 更新画布尺寸
        contentView.updateCanvasSize()
        
        // 更新合成图像
        Task {
            await contentView.processor.updateCompositeImage()
        }
    }
    
    // 开始自动保存
    private func startAutoSave(contentView: ContentView) {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: autoSaveInterval, repeats: true) { _ in
            performAutoSave(contentView: contentView, actionDescription: "自动保存")
        }
    }
    
    // 停止自动保存
    private func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
    
    // 执行自动保存
    private func performAutoSave(contentView: ContentView, actionDescription: String) {
        guard let currentProject = contentView.currentProject else { return }
        
        // 检查是否有变化（简单检查图层数量和修改时间）
        let now = Date()
        if now.timeIntervalSince(lastSaveTime) < 1.0 {
            return // 避免过于频繁的保存
        }
        
        // 保存编辑步骤
        projectManager.saveEditStep(
            for: currentProject.id,
            actionDescription: actionDescription,
            layers: contentView.processor.layers,
            canvasSize: contentView.canvasSize,
            aspectRatio: contentView.selectedAspectRatio
        )
        
        // 更新项目缩略图
        let thumbnailImage = contentView.generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: currentProject.id, image: thumbnailImage)
        
        lastSaveTime = now
        
        print("自动保存完成: \(actionDescription) - \(now)")
    }
}

// 扩展 ContentView 以支持项目管理
extension ContentView {
    var projectManager: ProjectManager? {
        get { objc_getAssociatedObject(self, &AssociatedKeys.projectManager) as? ProjectManager }
        set { objc_setAssociatedObject(self, &AssociatedKeys.projectManager, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
    
    var currentProject: Project? {
        get { objc_getAssociatedObject(self, &AssociatedKeys.currentProject) as? Project }
        set { objc_setAssociatedObject(self, &AssociatedKeys.currentProject, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
}

private struct AssociatedKeys {
    static var projectManager = "projectManager"
    static var currentProject = "currentProject"
}
